import { Card } from "@/components/ui/Card";
import { Separator } from "@/components/ui/Separator";

enum AttributeShortcode {
  strength = "STR",
  dexterity = "DEX",
  constitution = "CON",
  intelligence = "INT",
  wisdom = "WIS",
  charisma = "CHA",
}

enum SkillLabel {
  acrobatics = "Acrobatics",
  animalHandling = "Animal Handling",
  arcana = "Arcana",
  athletics = "Athletics",
  deception = "Deception",
  history = "History",
  insight = "Insight",
  intimidation = "Intimidation",
  investigation = "Investigation",
  medicine = "Medicine",
  nature = "Nature",
  perception = "Perception",
  performance = "Performance",
  persuasion = "Persuasion",
  religion = "Religion",
  sleightOfHand = "Sleight of Hand",
  stealth = "Stealth",
  survival = "Survival",
}

export type SkillKey =
  | "acrobatics"
  | "animalHandling"
  | "arcana"
  | "athletics"
  | "deception"
  | "history"
  | "insight"
  | "intimidation"
  | "investigation"
  | "medicine"
  | "nature"
  | "perception"
  | "performance"
  | "persuasion"
  | "religion"
  | "sleightOfHand"
  | "stealth"
  | "survival";

export const strengthSkills: SkillKey[] = ["athletics"];
export const dexteritySkills: SkillKey[] = ["acrobatics", "sleightOfHand", "stealth"];
export const constitutionSkills: SkillKey[] = [];
export const intelligenceSkills: SkillKey[] = [
  "arcana",
  "history",
  "investigation",
  "nature",
  "religion",
];
export const wisdomSkills: SkillKey[] = [
  "animalHandling",
  "insight",
  "medicine",
  "perception",
  "survival",
];
export const charismaSkills: SkillKey[] = [
  "deception",
  "intimidation",
  "performance",
  "persuasion",
];

export type AttributeKey = keyof typeof AttributeShortcode;

export type Attribute = {
  baseValue: number;
};

export type Attributes = {
  [key in AttributeKey]: Attribute;
};

export type Skill = {
  override?: number;
  value: number;
  modifier: number;
  label: string;
};

export type Skills = {
  [key in SkillKey]?: Skill;
};

const statblock = {
  name: "Acolyte",
  description:
    "Acolytes are junior members of a clergy, usually answerable to a priest. They perform a variety of functions in a temple and are granted minor spellcasting power by their deities.",
  attributes: {
    strength: {
      baseValue: 8,
    },
    dexterity: {
      baseValue: 10,
    },
    constitution: {
      baseValue: 12,
    },
    intelligence: {
      baseValue: 13,
    },
    wisdom: {
      baseValue: 14,
    },
    charisma: {
      baseValue: 20,
    },
  },
  skills: {},
};

const calculateSkillValue = ({
  attribute,
  skill,
  skillKey,
}: {
  attribute: Attribute;
  skill?: Skill;
  skillKey: SkillKey;
}) => {
  const value = attribute.baseValue + (skill?.override ?? 0);
  const modifier = Math.floor(value / 2) - 5;

  return {
    override: skill?.override || 0,
    value,
    modifier,
    label: SkillLabel[skillKey],
  };
};

const attributeSkillsMap: Record<AttributeKey, SkillKey[]> = {
  strength: strengthSkills,
  dexterity: dexteritySkills,
  constitution: constitutionSkills,
  intelligence: intelligenceSkills,
  wisdom: wisdomSkills,
  charisma: charismaSkills,
};

const calculateSkills = ({ attributes, skills }: { attributes: Attributes; skills: Skills }) => {
  const calculatedSkills: Skills = {};

  Object.entries(attributes).forEach(([attributeKey, attribute]) => {
    const skillKeys = attributeSkillsMap[attributeKey as AttributeKey];

    skillKeys.forEach((skillKey) => {
      const calculatedSkill = calculateSkillValue({
        attribute,
        skill: skills[skillKey],
        skillKey,
      });

      calculatedSkills[skillKey] = calculatedSkill;
    });
  });

  return calculatedSkills;
};

const getModifierLabel = (modifierValue: number) => {
  if (modifierValue === 0) {
    return "0";
  }

  if (modifierValue > 0) {
    return `+${modifierValue}`;
  }

  return modifierValue;
};

export const IndividualAttributeDisplay = ({
  attribute,
  value,
}: {
  attribute: AttributeShortcode;
  value: number;
}) => {
  return (
    <div className="border first:rounded-tl-lg border">
      <h3 className="scroll-m-20 text-xs font-semibold tracking-tight text-muted-foreground p-1 sm:p-2">
        {attribute}
      </h3>
      <Separator />
      <p className="text-lg p-1 sm:p-2">{value}</p>
      <Separator />
      <p className="text-sm p-1 sm:p-2">(+2)</p>
    </div>
  );
};

export const AttributeDisplay = ({ attributes }: { attributes: Attributes }) => {
  return (
    <div className="flex flex-col gap-4 items-center sm:items-start">
      <div className="grid grid-cols-6 w-fit text-center gap-0">
        {Object.entries(attributes).map(([key, value], index) => (
          <IndividualAttributeDisplay
            key={`${key}-${value}-${index}`}
            attribute={AttributeShortcode[key as keyof typeof AttributeShortcode]}
            value={value.baseValue}
          />
        ))}
      </div>
    </div>
  );
};

export const SkillsDisplay = ({
  attributes,
  skills,
}: {
  attributes: Attributes;
  skills: Skills;
}) => {
  const calculatedSkills = calculateSkills({ attributes, skills });
  const sortedSkills = Object.entries(calculatedSkills).sort((a, b) => {
    return a[1].label.localeCompare(b[1].label);
  });

  return (
    <div className="rounded-bl-lg border border-t-0 w-fit separator gap-0 divide-x text-sm w-full">
      {sortedSkills.map(([skillKey, skill]) => (
        <div key={skillKey} className="grid grid-cols-3 divide-x">
          <p className="p-1 text-start">{skill.label}</p>
          <p className="p-1 text-end">{skill.override || skill.value}</p>
          <p className="p-1 text-end">({getModifierLabel(skill.modifier)})</p>
        </div>
      ))}
    </div>
  );
};

export const StatblockDisplay = () => {
  return (
    <Card>
      <Card.Header>
        <Card.Title>
          <h3 className="scroll-m-20 text-2xl font-semibold tracking-tight">{statblock.name}</h3>
          <p className="text-muted-foreground text-sm">{statblock.description}</p>
        </Card.Title>
      </Card.Header>
      <Separator />
      <Card.Content>
        <div className="grid grid-cols-12 grid-flow-col">
          <div className="col-start-0 row-start-0 col-span-4">
            <AttributeDisplay attributes={statblock.attributes} />
          </div>
          <div className="col-start-0 row-start-2 col-span-4">
            <SkillsDisplay attributes={statblock.attributes} skills={statblock.skills} />
          </div>
        </div>
      </Card.Content>
    </Card>
  );
};
